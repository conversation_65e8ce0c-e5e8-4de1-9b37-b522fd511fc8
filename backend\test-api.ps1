# Test script for Workforce API
Write-Host "Testing Workforce API on http://localhost:5000" -ForegroundColor Green

# Test health endpoint
Write-Host "`nTesting health endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/v1/auth/health" -Method GET -UseBasicParsing
    Write-Host "Health endpoint status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "Health endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Swagger endpoint
Write-Host "`nTesting Swagger endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/swagger/index.html" -Method GET -UseBasicParsing
    Write-Host "Swagger endpoint status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Swagger page is accessible!" -ForegroundColor Cyan
} catch {
    Write-Host "Swagger endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Swagger JSON
Write-Host "`nTesting Swagger JSON..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/swagger/v1/swagger.json" -Method GET -UseBasicParsing
    Write-Host "Swagger JSON status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Swagger JSON is available!" -ForegroundColor Cyan
} catch {
    Write-Host "Swagger JSON failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nAPI Testing Complete!" -ForegroundColor Green
Write-Host "You can access the Swagger UI at: http://localhost:5000/swagger" -ForegroundColor Magenta
