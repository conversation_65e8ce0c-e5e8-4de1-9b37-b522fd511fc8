<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Workforce.API</name>
    </assembly>
    <members>
        <member name="T:Workforce.API.Controllers.AuthController">
            <summary>
            Controller for authentication operations
            </summary>
        </member>
        <member name="M:Workforce.API.Controllers.AuthController.Register(Workforce.Shared.DTOs.RegisterUserDto)">
            <summary>
            Registers a new user account
            </summary>
            <param name="registerUserDto">User registration data</param>
            <returns>Authenticated user with tokens</returns>
            <response code="201">User successfully registered</response>
            <response code="400">Invalid input data or validation errors</response>
            <response code="409">Email or phone number already exists</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:Workforce.API.Controllers.AuthController.Health">
            <summary>
            Health check endpoint for the authentication service
            </summary>
            <returns>Service health status</returns>
            <response code="200">Service is healthy</response>
        </member>
    </members>
</doc>
